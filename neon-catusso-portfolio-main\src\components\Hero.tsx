
import { Button } from "@/components/ui/button";
import { useEffect } from "react";

const Hero = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    // Starfield canvas animation
    const canvas = document.getElementById('starfield') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{x: number, y: number, vx: number, vy: number}> = [];

    // Create 30 particles
    for (let i = 0; i < 30; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.2,
        vy: (Math.random() - 0.5) * 0.2
      });
    }

    function animate() {
      ctx!.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Wrap around screen
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // Draw particle
        ctx!.fillStyle = '#9333ea';
        ctx!.shadowBlur = 10;
        ctx!.shadowColor = '#9333ea';
        ctx!.beginPath();
        ctx!.arc(particle.x, particle.y, 1, 0, Math.PI * 2);
        ctx!.fill();
      });

      requestAnimationFrame(animate);
    }

    animate();

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <section className="min-h-screen bg-[#0C0C0C] flex items-center justify-center px-4 py-8 relative overflow-hidden hero-synthwave">
      {/* Starfield canvas */}
      <canvas id="starfield" className="absolute inset-0 pointer-events-none" style={{ zIndex: -1 }}></canvas>

      <div className="container mx-auto grid lg:grid-cols-2 gap-12 items-center relative z-10">
        {/* Left side - Typography */}
        <div className="space-y-6 text-center lg:text-left">
          <div className="space-y-2">
            <h1 className="font-gt-flexa leading-tight">
              <div className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl tracking-tight synthwave-glow-yellow">CRIO</div>
              <div className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl text-neon-solar">EXPERIÊNCIAS</div>
              <div className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl tracking-tight synthwave-glow-yellow">DIGITAIS</div>
            </h1>
            <p className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-gt-flexa synthwave-glow-pink">
              MARCANTES
            </p>
          </div>

          <p className="text-text-white text-lg md:text-xl max-w-lg mx-auto lg:mx-0">
            Designer especializado em interfaces que aproximam pessoas das marcas
          </p>

          <Button
            onClick={scrollToContact}
            className="synthwave-btn"
            size="lg"
          >
            FALE AGORA
          </Button>
        </div>

        {/* Right side - Mockup */}
        <div className="flex justify-center lg:justify-end">
          <div className="relative">
            <div className="bg-dark-bg border-2 border-text-white p-8 rounded-3xl shadow-2xl max-w-sm">
              <div className="bg-tomato-red rounded-2xl p-6 text-center space-y-4">
                {/* Phone mockup header */}
                <div className="bg-dark-bg rounded-full h-6 w-20 mx-auto"></div>

                {/* App content */}
                <div className="text-dark-bg">
                  <h3 className="font-gt-flexa text-xl mb-2">Panzon</h3>
                  <p className="text-sm mb-4">Brathaming</p>

                  {/* Chef illustration placeholder */}
                  <div className="bg-text-white rounded-full h-20 w-20 mx-auto mb-4 flex items-center justify-center">
                    <div className="text-tomato-red text-2xl">👨‍🍳</div>
                  </div>

                  <h4 className="font-bold text-lg mb-4">Enstekia ldem</h4>

                  <Button className="bg-text-white text-dark-bg hover:bg-gray-100 font-bold py-2 px-6 rounded-full">
                    PEÇA AGORA
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
