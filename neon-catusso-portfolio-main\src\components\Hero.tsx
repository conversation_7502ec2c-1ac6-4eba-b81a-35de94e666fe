
import { Button } from "@/components/ui/button";

const Hero = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="min-h-screen bg-[#0C0C0C] flex items-center justify-center px-4 py-8 relative overflow-hidden">
      {/* Background noise effect */}
      <div className="absolute inset-0 opacity-[0.04] bg-noise"></div>

      {/* Animated neon particles */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-32 left-20 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-60 left-1/3 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-40 right-1/4 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute top-80 right-1/3 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse" style={{ animationDelay: '3s' }}></div>
        <div className="absolute bottom-60 left-1/4 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute top-1/3 left-3/4 w-0.5 h-0.5 bg-neon-solar rounded-full animate-pulse" style={{ animationDelay: '3.5s' }}></div>
      </div>

      <div className="container mx-auto grid lg:grid-cols-2 gap-12 items-center relative z-10">
        {/* Left side - Typography */}
        <div className="space-y-6 text-center lg:text-left">
          <div className="space-y-2">
            <h1 className="font-gt-flexa text-neon-solar leading-tight">
              <div className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl tracking-tight">CRIO</div>
              <div className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl">EXPERIÊNCIAS</div>
              <div className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl tracking-tight">DIGITAIS</div>
            </h1>
            <p className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-gt-flexa text-tomato-red neon-glow-red">
              MARCANTES
            </p>
          </div>

          <p className="text-text-white text-lg md:text-xl max-w-lg mx-auto lg:mx-0">
            Designer especializado em interfaces que aproximam pessoas das marcas
          </p>

          <Button
            onClick={scrollToContact}
            className="w-[200px] bg-[#0C0C0C] text-text-white border-2 border-neon-solar hover:bg-neon-solar hover:text-[#0C0C0C] transition-all duration-300 text-lg px-8 py-6 font-space-grotesk font-bold cursor-custom focus:ring-2 focus:ring-neon-solar focus:ring-offset-2 focus:ring-offset-[#0C0C0C]"
            size="lg"
          >
            FALE AGORA
          </Button>
        </div>

        {/* Right side - Mockup */}
        <div className="flex justify-center lg:justify-end">
          <div className="relative">
            <div className="bg-dark-bg border-2 border-text-white p-8 rounded-3xl shadow-2xl max-w-sm">
              <div className="bg-tomato-red rounded-2xl p-6 text-center space-y-4">
                {/* Phone mockup header */}
                <div className="bg-dark-bg rounded-full h-6 w-20 mx-auto"></div>

                {/* App content */}
                <div className="text-dark-bg">
                  <h3 className="font-gt-flexa text-xl mb-2">Panzon</h3>
                  <p className="text-sm mb-4">Brathaming</p>

                  {/* Chef illustration placeholder */}
                  <div className="bg-text-white rounded-full h-20 w-20 mx-auto mb-4 flex items-center justify-center">
                    <div className="text-tomato-red text-2xl">👨‍🍳</div>
                  </div>

                  <h4 className="font-bold text-lg mb-4">Enstekia ldem</h4>

                  <Button className="bg-text-white text-dark-bg hover:bg-gray-100 font-bold py-2 px-6 rounded-full">
                    PEÇA AGORA
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
