
import { useState, useEffect, useRef } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

const Portfolio = () => {
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalProgress, setModalProgress] = useState(0);
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);

  const projects = [
    {
      id: 1,
      title: "FinTech Revolution",
      category: "UX/UI Design",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop",
      challenge: "Redesenhar completamente a experiência de um app financeiro com 2 milhões de usuários, reduzindo a complexidade de 15 telas para 5 sem perder funcionalidades essenciais.",
      process: "Research com 200+ usuários › Análise de heatmaps › Card sorting › Prototipagem iterativa › Testes A/B com 10k usuários › Design system completo › Handoff para desenvolvimento.",
      impact: "Redução de 60% no tempo de conclusão de tarefas, aumento de 45% na retenção mensal e NPS saltou de 6.2 para 8.7. Economia de R$ 2.3M anuais em suporte ao cliente."
    },
    {
      id: 2,
      title: "E-commerce Sustentável",
      category: "Branding + UX",
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop",
      challenge: "Criar identidade visual e experiência digital para startup de produtos eco-friendly, competindo com gigantes do mercado sem orçamento para mídia paga.",
      process: "Brand strategy › Pesquisa etnográfica › Sistema de identidade › Arquitetura de informação › Design responsivo › Otimização para conversão › Implementação no Shopify.",
      impact: "Lançamento com 15k visitantes únicos no primeiro mês, taxa de conversão de 4.2% (média do setor: 2.1%) e reconhecimento no Awwwards. Faturamento de R$ 180k no primeiro trimestre."
    },
    {
      id: 3,
      title: "SaaS B2B Dashboard",
      category: "Design System",
      image: "https://images.unsplash.com/photo-1497604401993-f2e922e5cb0a?w=400&h=300&fit=crop",
      challenge: "Unificar 8 produtos diferentes de uma empresa de software em um design system escalável, mantendo a identidade única de cada produto mas criando consistência.",
      process: "Auditoria de componentes › Atomic design › Tokens de design › Biblioteca no Figma › Documentação técnica › Treinamento de 3 equipes › Implementação gradual.",
      impact: "Redução de 70% no tempo de desenvolvimento de novas features, padronização de 150+ componentes e economia de 40 horas/semana da equipe de design. ROI de 300% em 6 meses."
    },
    {
      id: 4,
      title: "App Delivery Gamificado",
      category: "Mobile UX",
      image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=300&fit=crop",
      challenge: "Criar experiência mobile que diferenciasse um novo app de delivery em São Paulo, onde já existiam 12 concorrentes estabelecidos, focando em gamificação sem comprometer usabilidade.",
      process: "Benchmark competitivo › User journey mapping › Wireframes de baixa/alta fidelidade › Prototipagem interativa › Testes de usabilidade com 50 usuários › Iterações baseadas em feedback.",
      impact: "App Store rating 4.9/5, 100k downloads em 30 dias, tempo médio de sessão 40% maior que concorrentes. Destaque como 'App da Semana' na App Store brasileira."
    },
    {
      id: 5,
      title: "Plataforma Telemedicina",
      category: "UX Research",
      image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop",
      challenge: "Desenvolver interface de telemedicina acessível para pacientes de 18 a 80+ anos, considerando diferentes níveis de letramento digital e necessidades de acessibilidade.",
      process: "Pesquisa com 300 pacientes › Personas detalhadas › Testes de acessibilidade › Design inclusivo › Prototipagem com feedback médico › Validação com grupos focais › Iterações contínuas.",
      impact: "25k consultas realizadas no primeiro mês, 94% de satisfação dos pacientes, redução de 50% no tempo de agendamento. Certificação de acessibilidade WCAG 2.1 AA."
    },
    {
      id: 6,
      title: "Portal Educacional",
      category: "Web Design",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop",
      challenge: "Redesign completo de plataforma educacional com 500k alunos, melhorando engajamento e reduzindo taxa de abandono de cursos que estava em 68%.",
      process: "Analytics profundo › Heatmaps e session recordings › Entrevistas com professores e alunos › Novo sistema de navegação › Gamificação sutil › Testes multivariados › Implementação progressiva.",
      impact: "Taxa de abandono reduziu para 31%, tempo de permanência aumentou 85%, conclusão de cursos subiu 52%. Economia de R$ 1.8M anuais em retenção de alunos."
    }
  ];

  const openModal = (projectId: number) => {
    setSelectedProject(projectId);
    setIsModalOpen(true);
    document.body.style.overflow = 'hidden';

    // Update progress bar
    const currentIndex = projects.findIndex(p => p.id === projectId);
    setModalProgress(((currentIndex + 1) / projects.length) * 100);

    // Focus trap - focus close button after modal opens
    setTimeout(() => {
      closeButtonRef.current?.focus();
    }, 100);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedProject(null);
    document.body.style.overflow = 'unset';
    setModalProgress(0);
  };

  const navigateProject = (direction: 'prev' | 'next') => {
    if (selectedProject === null) return;

    const currentIndex = projects.findIndex(p => p.id === selectedProject);
    let newIndex;

    if (direction === 'prev') {
      newIndex = currentIndex === 0 ? projects.length - 1 : currentIndex - 1;
    } else {
      newIndex = currentIndex === projects.length - 1 ? 0 : currentIndex + 1;
    }

    setSelectedProject(projects[newIndex].id);

    // Update progress bar
    setModalProgress(((newIndex + 1) / projects.length) * 100);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isModalOpen) return;

      if (e.key === 'Escape') {
        closeModal();
      } else if (e.key === 'ArrowLeft') {
        navigateProject('prev');
      } else if (e.key === 'ArrowRight') {
        navigateProject('next');
      } else if (e.key === 'Tab') {
        // Focus trap - keep focus within modal
        e.preventDefault();
        const focusableElements = modalRef.current?.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (focusableElements && focusableElements.length > 0) {
          const firstElement = focusableElements[0] as HTMLElement;
          const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              lastElement.focus();
            }
          } else {
            if (document.activeElement === lastElement) {
              firstElement.focus();
            }
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isModalOpen, selectedProject]);

  const currentProject = projects.find(p => p.id === selectedProject);

  return (
    <>
      <section id="portfolio" className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <h2 className="font-gt-flexa text-dark-bg text-4xl md:text-5xl text-center mb-12">
            PROJETOS QUE MUDARAM O JOGO
          </h2>

          {/* Masonry Grid */}
          <div className="columns-1 md:columns-2 lg:columns-3 gap-6 max-w-6xl mx-auto space-y-6">
            {projects.map((project) => (
              <div
                key={project.id}
                className="break-inside-avoid cursor-custom"
                onClick={() => openModal(project.id)}
              >
                <div className="w-80 h-56 border-2 border-dark-bg rounded bg-white overflow-hidden transition-all duration-200 hover:translate-y-1 hover:shadow-[0_4px_0_#FF3B30] group">
                  <div className="relative w-full h-full">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-full object-cover transition-all duration-300"
                      style={{
                        filter: 'sepia(1) saturate(2) hue-rotate(180deg) brightness(0.8) contrast(1.2)'
                      }}
                      loading="lazy"
                      decoding="async"
                    />
                    <div className="absolute bottom-0 left-0 bg-tomato-red bg-opacity-80 text-text-white text-xs px-2 py-1">
                      {project.category}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Upgraded Project Modal */}
      {isModalOpen && currentProject && (
        <div
          className="fixed inset-0 z-50 bg-black bg-opacity-80 flex items-center justify-center p-4"
          role="dialog"
          aria-label={currentProject.title}
          aria-modal="true"
        >
          {/* Modal content with slide-up + fade animation */}
          <div
            ref={modalRef}
            className="relative w-[90vw] md:w-full md:max-w-[760px] bg-dark-bg rounded-lg overflow-hidden modal-slide-up"
            style={{ backgroundColor: '#0F0F1C' }}
          >
            {/* Close button */}
            <button
              ref={closeButtonRef}
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 text-neon-solar hover:text-tomato-red transition-colors p-2 bg-dark-bg rounded-full"
              aria-label="Fechar modal"
            >
              <X size={24} />
            </button>

            {/* Navigation arrows - fixed sides with glow */}
            <button
              onClick={() => navigateProject('prev')}
              className="fixed left-4 top-1/2 -translate-y-1/2 text-neon-solar hover:text-tomato-red transition-colors p-2 nav-arrow-glow"
              aria-label="Projeto anterior"
              style={{ zIndex: 60 }}
            >
              <ChevronLeft size={32} />
            </button>

            <button
              onClick={() => navigateProject('next')}
              className="fixed right-4 top-1/2 -translate-y-1/2 text-neon-solar hover:text-tomato-red transition-colors p-2 nav-arrow-glow"
              aria-label="Próximo projeto"
              style={{ zIndex: 60 }}
            >
              <ChevronRight size={32} />
            </button>

            {/* Hero image 16:9 with border */}
            <div className="relative w-full" style={{ aspectRatio: '16/9' }}>
              <img
                src={currentProject.image}
                alt={currentProject.title}
                className="w-full h-full object-cover border-4"
                style={{ borderColor: '#FCEC07' }}
                loading="lazy"
              />
            </div>

            {/* Content area */}
            <div className="p-6 md:p-8">
              {/* Project title with underline */}
              <h2
                className="font-gt-flexa text-3xl mb-8 text-center project-title-underline"
                style={{
                  color: '#FCEC07',
                  fontSize: '32px'
                }}
              >
                {currentProject.title}
              </h2>

              {/* Content cards */}
              <div className="space-y-6">
                {/* DESAFIO Card */}
                <div className="bg-dark-bg p-6 rounded-lg" style={{ marginBlock: '24px' }}>
                  <h3
                    className="font-gt-flexa mb-4 subtitle-hover cursor-pointer"
                    style={{
                      color: '#FF3B30',
                      fontSize: '20px',
                      letterSpacing: '0.5px'
                    }}
                  >
                    DESAFIO
                  </h3>
                  <p
                    className="text-white"
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.6'
                    }}
                  >
                    {currentProject.challenge}
                  </p>
                </div>

                {/* PROCESSO Card */}
                <div className="bg-dark-bg p-6 rounded-lg" style={{ marginBlock: '24px' }}>
                  <h3
                    className="font-gt-flexa mb-4 subtitle-hover cursor-pointer"
                    style={{
                      color: '#FF3B30',
                      fontSize: '20px',
                      letterSpacing: '0.5px'
                    }}
                  >
                    PROCESSO
                  </h3>
                  <p
                    className="text-white"
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.6'
                    }}
                  >
                    {currentProject.process}
                  </p>
                </div>

                {/* IMPACTO Card */}
                <div className="bg-dark-bg p-6 rounded-lg" style={{ marginBlock: '24px' }}>
                  <h3
                    className="font-gt-flexa mb-4 subtitle-hover cursor-pointer"
                    style={{
                      color: '#FF3B30',
                      fontSize: '20px',
                      letterSpacing: '0.5px'
                    }}
                  >
                    IMPACTO
                  </h3>
                  <p
                    className="text-white"
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.6'
                    }}
                  >
                    {currentProject.impact}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div
            className="modal-progress"
            style={{ width: `${modalProgress}%` }}
          ></div>
        </div>
      )}
    </>
  );
};

export default Portfolio;
