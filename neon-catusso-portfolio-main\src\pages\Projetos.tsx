
import Navigation from "@/components/Navigation";
import ThemeToggle from "@/components/ThemeToggle";

const Projetos = () => {
  const projects = [
    {
      id: 1,
      title: "FinTech Revolution",
      category: "UX/UI Design",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600&h=400&fit=crop",
      description: "Redesign de app financeiro que reduziu 60% do tempo de tarefas e aumentou retenção em 45%"
    },
    {
      id: 2,
      title: "E-commerce Sustentável",
      category: "Branding + UX",
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=600&h=400&fit=crop",
      description: "Identidade e experiência digital que alcançou 4.2% de conversão e reconhecimento no Awwwards"
    },
    {
      id: 3,
      title: "SaaS B2B Dashboard",
      category: "Design System",
      image: "https://images.unsplash.com/photo-1497604401993-f2e922e5cb0a?w=600&h=400&fit=crop",
      description: "Sistema unificado que reduziu 70% do tempo de desenvolvimento e gerou ROI de 300%"
    },
    {
      id: 4,
      title: "App Delivery Gamificado",
      category: "Mobile UX",
      image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=600&h=400&fit=crop",
      description: "App com 4.9/5 na App Store, 100k downloads em 30 dias e destaque como 'App da Semana'"
    },
    {
      id: 5,
      title: "Plataforma Telemedicina",
      category: "UX Research",
      image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=600&h=400&fit=crop",
      description: "Interface acessível que realizou 25k consultas no primeiro mês com 94% de satisfação"
    },
    {
      id: 6,
      title: "Portal Educacional",
      category: "Web Design",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop",
      description: "Redesign que reduziu abandono de 68% para 31% e aumentou conclusões em 52%"
    }
  ];

  const categories = ["Todos", "UX/UI Design", "Design System", "Branding + UX", "Mobile UX", "UX Research", "Web Design"];

  return (
    <div className="min-h-screen bg-white dark:bg-dark-bg">
      <Navigation />
      <ThemeToggle />

      <div className="container mx-auto px-4 py-16 md:pl-32">
        <header className="mb-12">
          <h1 className="font-gt-flexa text-4xl md:text-6xl text-dark-bg dark:text-neon-solar mb-4">
            CASES DE SUCESSO COMPROVADO
          </h1>
          <p className="text-lg text-dark-bg dark:text-text-white max-w-2xl">
            Cada projeto é uma oportunidade de revolucionar experiências digitais e gerar resultados mensuráveis.
          </p>
        </header>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-4 mb-8">
          {categories.map((category) => (
            <button
              key={category}
              className="px-4 py-2 font-gt-flexa text-sm border-2 border-dark-bg dark:border-neon-solar text-dark-bg dark:text-neon-solar hover:bg-neon-solar hover:text-dark-bg transition-colors cursor-custom"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <div
              key={project.id}
              className="neo-brutal-card group cursor-custom"
            >
              <div className="overflow-hidden mb-4">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  loading="lazy"
                />
              </div>
              <div className="p-4">
                <div className="text-sm text-tomato-red font-space-grotesk font-medium mb-2">
                  {project.category}
                </div>
                <h3 className="font-gt-flexa text-xl text-dark-bg mb-2">
                  {project.title}
                </h3>
                <p className="text-dark-bg text-sm">
                  {project.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Projetos;
