@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 100% 98%;
    --foreground: 0 0% 6%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 6%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 6%;

    --primary: 60 100% 51%;
    --primary-foreground: 0 0% 6%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 6%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 6%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 60 100% 51%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom color variables for Neo-Brutal theme */
    --neon-solar: #FCEC07;
    --dark-bg: #101010;
    --tomato-red: #FF3B30;
    --text-white: #FFFFFF;
    --gray-border: #9E9E9E;
  }

  .dark {
    --background: 0 0% 6%;
    --foreground: 0 0% 98%;

    --card: 0 0% 6%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 6%;
    --popover-foreground: 0 0% 98%;

    --primary: 60 100% 51%;
    --primary-foreground: 0 0% 6%;

    --secondary: 0 0% 11%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 11%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 11%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 11%;
    --input: 0 0% 11%;
    --ring: 60 100% 51%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-space-grotesk;
  }

  /* Custom focus styles for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-neon-solar ring-offset-2 ring-offset-dark-bg;
    outline: 2px dashed #FCEC07;
  }

  /* Custom cursor for interactive elements */
  .cursor-custom {
    cursor: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' x='1' y='1' fill='none' stroke='%23FCEC07' stroke-width='2'/%3E%3C/svg%3E") 10 10, auto;
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #101010;
  }

  ::-webkit-scrollbar-thumb {
    background: #FCEC07;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #FF3B30;
  }
}

@layer components {
  .neo-brutal-card {
    @apply bg-white border-2 border-dark-bg transition-all duration-200 ease-out;
  }

  .neo-brutal-card:hover {
    @apply transform translate-y-1;
    box-shadow: 0 4px 0 #FF3B30;
  }

  .neon-glow {
    text-shadow: 0 0 10px #FCEC07, 0 0 20px #FCEC07, 0 0 30px #FCEC07;
  }

  .neon-glow-red {
    text-shadow: 0 0 6px #FF3B30, 0 0 12px #FF3B30, 0 0 18px #FF3B30;
  }

  /* Background noise effect */
  .bg-noise {
    background-image:
      radial-gradient(circle at 25% 25%, #FCEC07 0.5px, transparent 0.5px),
      radial-gradient(circle at 75% 75%, #FCEC07 0.5px, transparent 0.5px);
    background-size: 50px 50px;
    background-position: 0 0, 25px 25px;
  }

  .diagonal-strip {
    transform: skewY(-8deg);
    transform-origin: top left;
  }

  /* CTA Diagonal Strip */
  .cta-diagonal {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    width: 100vw;
    height: 220px;
    background: linear-gradient(45deg, #FF3B30 0%, #F94646 100%);
    transform: skewY(-4deg);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .cta-content {
    transform: skewY(4deg);
    text-align: center;
    color: #FFFFFF;
    max-width: 720px;
    padding: 0 24px;
  }

  .cta-content h3 {
    font-family: "GT Flexa", sans-serif;
    font-weight: 700;
    font-size: 24px;
    line-height: 1.2;
    margin: 0 0 8px;
  }

  .cta-content p {
    font-family: "Space Grotesk", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.4;
    margin: 0 0 24px;
  }

  .cta-content button {
    background: #FFFFFF;
    color: #101010;
    font-family: "Space Grotesk", sans-serif;
    font-weight: 700;
    font-size: 14px;
    padding: 12px 32px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
  }

  .cta-content button:hover {
    background: #FCEC07;
  }

  @media (max-width: 600px) {
    .cta-diagonal {
      height: 260px;
    }

    .cta-content h3 {
      font-size: 20px;
    }
  }

  /* Testimonial card glow effect */
  .testimonial-card-glow {
    box-shadow: 0 0 20px rgba(252, 236, 7, 0.3);
  }

  /* Enhanced Modal Styles */
  .modal-backdrop {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
  }

  .modal-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
  }

  .modal-scale-fade {
    animation: modalScaleFade 450ms ease-out;
  }

  @keyframes modalScaleFade {
    0% {
      opacity: 0;
      transform: scale(0.96);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Modal Close Button */
  .modal-close-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(15, 15, 28, 0.9);
    color: #FCEC07;
    border: 2px solid #FCEC07;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .modal-close-btn:hover {
    box-shadow: 0 4px 12px rgba(252, 236, 7, 0.4);
    transform: scale(1.05);
  }

  /* Modal Hero Image */
  .modal-hero-container {
    width: 100%;
    height: 260px;
    position: relative;
    overflow: hidden;
  }

  .modal-hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-bottom: 4px solid #FCEC07;
    transition: opacity 0.6s ease;
  }

  @media (max-width: 768px) {
    .modal-hero-container {
      height: 200px;
    }
  }

  /* Modal Content Body */
  .modal-content-body {
    padding: 40px 48px;
  }

  @media (max-width: 768px) {
    .modal-content-body {
      padding: 32px 24px;
    }
  }

  /* Modal Title */
  .modal-title {
    font-family: "GT Flexa", sans-serif;
    font-weight: 700;
    font-size: 32px;
    line-height: 1.2;
    color: #FCEC07;
    text-align: center;
    margin-bottom: 32px;
    position: relative;
  }

  .modal-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 56px;
    height: 3px;
    background-color: #FF3B30;
  }

  /* Modal Subtitle */
  .modal-subtitle {
    font-family: "GT Flexa", sans-serif;
    font-weight: 700;
    font-size: 18px;
    color: #FCEC07;
    margin-bottom: 16px;
    letter-spacing: 0.5px;
  }

  /* Modal Text */
  .modal-text {
    font-family: "Space Grotesk", sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #E0E0E0;
    max-width: 90ch;
  }

  /* Enhanced Modal Navigation Arrows */
  .modal-nav-arrow {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(16, 16, 16, 0.6);
    color: #FCEC07;
    border: 2px solid #FCEC07;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .modal-nav-arrow:hover {
    box-shadow: 0 0 20px rgba(252, 236, 7, 0.6);
    transform: scale(1.05);
    background: rgba(16, 16, 16, 0.8);
  }

  .modal-nav-vibrate {
    animation: navVibrate 8s ease-in-out infinite;
  }

  @keyframes navVibrate {
    0%, 95%, 100% {
      transform: translateX(0);
    }
    97.5% {
      transform: translateX(2px);
    }
  }

  /* Progress Indicator */
  .modal-progress-indicator {
    position: absolute;
    bottom: 16px;
    right: 24px;
    font-family: "Space Grotesk", sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #FCEC07;
    background: rgba(15, 15, 28, 0.9);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #FCEC07;
  }

  /* Progress bar */
  .modal-progress {
    position: fixed;
    bottom: 8%;
    left: 50%;
    transform: translateX(-50%);
    height: 4px;
    background: #FCEC07;
    transition: width 0.4s ease-out;
    z-index: 60;
  }

  /* CRT scan-line animation */
  .crt-scanlines {
    background: linear-gradient(
      transparent 50%,
      rgba(252, 236, 7, 0.03) 50%
    );
    background-size: 100% 4px;
    animation: scanlines 120ms linear infinite;
  }

  @keyframes scanlines {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 0 4px;
    }
  }

  /* Page transition styles */
  .page-transition {
    position: fixed;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(8deg, #FCEC07 0%, #FCEC07 100%);
    z-index: 9999;
    animation: pageWipe 0.6s ease-in-out;
  }

  @keyframes pageWipe {
    0% {
      left: -100%;
      transform: skewY(-8deg);
    }
    50% {
      left: 0%;
      transform: skewY(-8deg);
    }
    100% {
      left: 100%;
      transform: skewY(-8deg);
    }
  }

  /* Custom color utilities */
  .bg-neon-solar {
    background-color: var(--neon-solar);
  }

  .text-neon-solar {
    color: var(--neon-solar);
  }

  .border-neon-solar {
    border-color: var(--neon-solar);
  }

  .bg-dark-bg {
    background-color: var(--dark-bg);
  }

  .text-dark-bg {
    color: var(--dark-bg);
  }

  .border-dark-bg {
    border-color: var(--dark-bg);
  }

  .bg-tomato-red {
    background-color: var(--tomato-red);
  }

  .text-tomato-red {
    color: var(--tomato-red);
  }

  .border-tomato-red {
    border-color: var(--tomato-red);
  }

  .text-text-white {
    color: var(--text-white);
  }

  .border-gray-border {
    border-color: var(--gray-border);
  }

  /* Typography utilities */
  .font-gt-flexa {
    font-family: 'Inter', sans-serif;
    font-weight: 900;
  }

  .font-space-grotesk {
    font-family: 'Space Grotesk', sans-serif;
  }
}
