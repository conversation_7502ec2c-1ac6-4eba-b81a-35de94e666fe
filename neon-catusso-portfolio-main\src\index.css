@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 100% 98%;
    --foreground: 0 0% 6%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 6%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 6%;

    --primary: 60 100% 51%;
    --primary-foreground: 0 0% 6%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 6%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 6%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 60 100% 51%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom color variables for Neo-Brutal theme */
    --neon-solar: #FCEC07;
    --dark-bg: #101010;
    --tomato-red: #FF3B30;
    --text-white: #FFFFFF;
    --gray-border: #9E9E9E;
  }

  .dark {
    --background: 0 0% 6%;
    --foreground: 0 0% 98%;

    --card: 0 0% 6%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 6%;
    --popover-foreground: 0 0% 98%;

    --primary: 60 100% 51%;
    --primary-foreground: 0 0% 6%;

    --secondary: 0 0% 11%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 11%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 11%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 11%;
    --input: 0 0% 11%;
    --ring: 60 100% 51%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-space-grotesk;
  }

  /* Custom focus styles for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-neon-solar ring-offset-2 ring-offset-dark-bg;
    outline: 2px dashed #FCEC07;
  }

  /* Custom cursor for interactive elements */
  .cursor-custom {
    cursor: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' x='1' y='1' fill='none' stroke='%23FCEC07' stroke-width='2'/%3E%3C/svg%3E") 10 10, auto;
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #101010;
  }

  ::-webkit-scrollbar-thumb {
    background: #FCEC07;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #FF3B30;
  }
}

@layer components {
  .neo-brutal-card {
    @apply bg-white border-2 border-dark-bg transition-all duration-200 ease-out;
  }

  .neo-brutal-card:hover {
    @apply transform translate-y-1;
    box-shadow: 0 4px 0 #FF3B30;
  }

  .neon-glow {
    text-shadow: 0 0 10px #FCEC07, 0 0 20px #FCEC07, 0 0 30px #FCEC07;
  }

  .neon-glow-red {
    text-shadow: 0 0 6px #FF3B30, 0 0 12px #FF3B30, 0 0 18px #FF3B30;
  }

  /* Background noise effect */
  .bg-noise {
    background-image:
      radial-gradient(circle at 25% 25%, #FCEC07 0.5px, transparent 0.5px),
      radial-gradient(circle at 75% 75%, #FCEC07 0.5px, transparent 0.5px);
    background-size: 50px 50px;
    background-position: 0 0, 25px 25px;
  }

  .diagonal-strip {
    transform: skewY(-8deg);
    transform-origin: top left;
  }

  /* Synthwave Background Grid */
  #neonGrid {
    position: fixed;
    inset: 0;
    z-index: -2;
    width: 100vw;
    height: 100vh;
    display: block;
    background: radial-gradient(ellipse at 50% 120%, #0f0020 0%, #060012 60%, #010006 100%);
  }

  /* Synthwave Hero Styles */
  .hero-synthwave::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(transparent 95%, rgba(33, 33, 255, 0.2) 95%) 0 0/6px 6px;
    pointer-events: none;
    z-index: 1;
  }

  .synthwave-glow-yellow {
    color: #FCEC07;
    text-shadow: 0 0 12px #FCEC07, 0 0 32px rgba(252, 236, 7, 0.33);
  }

  .synthwave-glow-pink {
    color: #FF4C9A;
    text-shadow: 0 0 12px #FF4C9A, 0 0 32px rgba(255, 76, 154, 0.33);
  }

  .synthwave-btn {
    width: 200px;
    background: transparent;
    color: #FFFFFF;
    border: 2px solid #00C8FF;
    border-radius: 6px;
    padding: 12px 32px;
    font-family: "Space Grotesk", sans-serif;
    font-weight: 700;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
  }

  .synthwave-btn:hover {
    background: #00C8FF;
    color: #101010;
    box-shadow: 0 0 20px rgba(0, 200, 255, 0.5);
  }

  /* Neon Phone Styles */
  .neon-phone {
    width: 240px;
    height: 520px;
    perspective: 800px;
    margin: 0 auto;
    cursor: pointer;
  }

  .neon-phone .np-frame {
    position: relative;
    width: 100%;
    height: 100%;
    border: 2px solid #00C8FF;
    border-radius: 32px;
    box-shadow: 0 0 12px #00C8FF, 0 0 24px rgba(0, 200, 255, 0.33);
    transform-style: preserve-3d;
    transition: transform 0.4s ease;
    background: #000;
  }

  .neon-phone .np-notch {
    position: absolute;
    top: 36px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 12px;
    border-radius: 6px;
    background: #101010;
  }

  .np-screen {
    position: absolute;
    top: 80px;
    left: 10%;
    width: 80%;
    height: 70%;
    background: linear-gradient(160deg, #FF4C9A 0%, #FCEC07 100%);
    border-radius: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: inset 0 0 24px rgba(0, 0, 0, 0.5);
  }

  .np-logo {
    font-family: 'GT Flexa', sans-serif;
    font-weight: 700;
    font-size: 42px;
    line-height: 1;
    color: #FFFFFF;
    text-shadow: 0 0 6px #FFFFFF;
    margin-bottom: 32px;
  }

  .np-btn {
    background: #101010;
    color: #FCEC07;
    border: 2px solid #FCEC07;
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    font-size: 14px;
    padding: 12px 24px;
    border-radius: 24px;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
  }

  .np-btn:hover {
    background: #FCEC07;
    color: #101010;
  }

  /* CTA Horizontal Strip */
  .cta-strip {
    position: relative;
    width: 100%;
    height: 80px;
    background: #101010;
    display: flex;
    align-items: center;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  /* Texto em loop deslizante */
  .ticker {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    white-space: nowrap;
  }

  .ticker span {
    font-family: "GT Flexa", sans-serif;
    font-weight: 700;
    font-size: 32px;
    line-height: 1;
    color: #FCEC07;
    animation: scroll 20s linear infinite;
    display: flex;
    align-items: center;
    gap: 24px;
  }

  @keyframes scroll {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(100%);
    }
  }

  /* Botão dentro do ticker */
  .ticker-btn {
    background: #FCEC07;
    color: #101010;
    border: none;
    padding: 8px 20px;
    font-family: "Space Grotesk", sans-serif;
    font-weight: 700;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 4px;
    white-space: nowrap;
    margin: 0 8px;
  }

  .ticker-btn:hover {
    background: #FF3B30;
    color: #FFFFFF;
    transform: scale(1.05);
  }

  @media (max-width: 600px) {
    .cta-strip {
      height: 60px;
    }

    .ticker span {
      font-size: 24px;
      gap: 16px;
    }

    .ticker-btn {
      padding: 6px 16px;
      font-size: 12px;
      margin: 0 6px;
    }
  }

  /* Testimonial card glow effect */
  .testimonial-card-glow {
    box-shadow: 0 0 20px rgba(252, 236, 7, 0.3);
  }

  /* Enhanced Modal Styles */
  .modal-backdrop {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
  }

  .modal-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
  }

  .modal-scale-fade {
    animation: modalScaleFade 450ms ease-out;
  }

  @keyframes modalScaleFade {
    0% {
      opacity: 0;
      transform: scale(0.96);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Modal Close Button */
  .modal-close-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(15, 15, 28, 0.9);
    color: #FCEC07;
    border: 2px solid #FCEC07;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .modal-close-btn:hover {
    box-shadow: 0 4px 12px rgba(252, 236, 7, 0.4);
    transform: scale(1.05);
  }

  /* Modal Hero Image */
  .modal-hero-container {
    width: 100%;
    height: 260px;
    position: relative;
    overflow: hidden;
  }

  .modal-hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-bottom: 4px solid #FCEC07;
    transition: opacity 0.6s ease;
  }

  @media (max-width: 768px) {
    .modal-hero-container {
      height: 200px;
    }
  }

  /* Modal Content Body */
  .modal-content-body {
    padding: 40px 48px;
  }

  @media (max-width: 768px) {
    .modal-content-body {
      padding: 32px 24px;
    }
  }

  /* Modal Title */
  .modal-title {
    font-family: "GT Flexa", sans-serif;
    font-weight: 700;
    font-size: 32px;
    line-height: 1.2;
    color: #FCEC07;
    text-align: center;
    margin-bottom: 32px;
    position: relative;
  }

  .modal-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 56px;
    height: 3px;
    background-color: #FF3B30;
  }

  /* Modal Subtitle */
  .modal-subtitle {
    font-family: "GT Flexa", sans-serif;
    font-weight: 700;
    font-size: 18px;
    color: #FCEC07;
    margin-bottom: 16px;
    letter-spacing: 0.5px;
  }

  /* Modal Text */
  .modal-text {
    font-family: "Space Grotesk", sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #E0E0E0;
    max-width: 90ch;
  }

  /* Enhanced Modal Navigation Arrows */
  .modal-nav-arrow {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(16, 16, 16, 0.6);
    color: #FCEC07;
    border: 2px solid #FCEC07;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .modal-nav-arrow:hover {
    box-shadow: 0 0 20px rgba(252, 236, 7, 0.6);
    transform: scale(1.05);
    background: rgba(16, 16, 16, 0.8);
  }

  .modal-nav-vibrate {
    animation: navVibrate 8s ease-in-out infinite;
  }

  @keyframes navVibrate {
    0%, 95%, 100% {
      transform: translateX(0);
    }
    97.5% {
      transform: translateX(2px);
    }
  }

  /* Progress Indicator */
  .modal-progress-indicator {
    position: absolute;
    bottom: 16px;
    right: 24px;
    font-family: "Space Grotesk", sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #FCEC07;
    background: rgba(15, 15, 28, 0.9);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #FCEC07;
  }

  /* Progress bar */
  .modal-progress {
    position: fixed;
    bottom: 8%;
    left: 50%;
    transform: translateX(-50%);
    height: 4px;
    background: #FCEC07;
    transition: width 0.4s ease-out;
    z-index: 60;
  }

  /* CRT scan-line animation */
  .crt-scanlines {
    background: linear-gradient(
      transparent 50%,
      rgba(252, 236, 7, 0.03) 50%
    );
    background-size: 100% 4px;
    animation: scanlines 120ms linear infinite;
  }

  @keyframes scanlines {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 0 4px;
    }
  }

  /* Page transition styles */
  .page-transition {
    position: fixed;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(8deg, #FCEC07 0%, #FCEC07 100%);
    z-index: 9999;
    animation: pageWipe 0.6s ease-in-out;
  }

  @keyframes pageWipe {
    0% {
      left: -100%;
      transform: skewY(-8deg);
    }
    50% {
      left: 0%;
      transform: skewY(-8deg);
    }
    100% {
      left: 100%;
      transform: skewY(-8deg);
    }
  }

  /* Custom color utilities */
  .bg-neon-solar {
    background-color: var(--neon-solar);
  }

  .text-neon-solar {
    color: var(--neon-solar);
  }

  .border-neon-solar {
    border-color: var(--neon-solar);
  }

  .bg-dark-bg {
    background-color: var(--dark-bg);
  }

  .text-dark-bg {
    color: var(--dark-bg);
  }

  .border-dark-bg {
    border-color: var(--dark-bg);
  }

  .bg-tomato-red {
    background-color: var(--tomato-red);
  }

  .text-tomato-red {
    color: var(--tomato-red);
  }

  .border-tomato-red {
    border-color: var(--tomato-red);
  }

  .text-text-white {
    color: var(--text-white);
  }

  .border-gray-border {
    border-color: var(--gray-border);
  }

  /* Typography utilities */
  .font-gt-flexa {
    font-family: 'Inter', sans-serif;
    font-weight: 900;
  }

  .font-space-grotesk {
    font-family: 'Space Grotesk', sans-serif;
  }
}
