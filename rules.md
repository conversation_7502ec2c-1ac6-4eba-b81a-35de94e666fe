### RULEBOOK START
❖ CONTEXTO
Portfólio do designer <PERSON><PERSON><PERSON> estilo “Neo-Brutal Neon”.
Estrutura atual: Home, Projetos, Sobre, Serviços, Contato, 404.
Paleta: #FCEC07 / #0F0F1C / #FF3B30 / #FFFFFF / #9E9E9E.
Fontes: GT Flexa Black (headings), Space Grotesk (body).

❖ CONTEÚDO IMPLEMENTADO
• PARTE 1 ✅: Homepage - Headlines & Bio atualizados
  - Hero: "CRIO EXPERIÊNCIAS DIGITAIS MARCANTES" + sub-headline otimizado
  - CTA: "FALE AGORA" (3 palavras)
  - Bio: 12 anos experiência, São Paulo, paixão tipografia
  - SEO: Title e description otimizados

• DEPOIMENTOS ✅: Container 100% width, fundo #0F0F1C
  - Cards 600×220px, borda 2px #FCEC07 com glow
  - Foto 64px circular, borda 2px #FF3B30
  - Auto-slider 5s, dots neon-yellow/cinza

• CTA STRIP HORIZONTAL ✅: Substituiu faixa diagonal
  - Posição: Entre Hero e About (topo da página)
  - Background: #101010, altura 80px desktop/60px mobile
  - Texto deslizante: "VAMOS CONVERSAR?" GT Flexa 32px #FCEC07
  - Botões integrados: "FALE COMIGO" deslizam com o texto
  - Animação: scroll 20s linear infinite, esquerda→direita
  - Hover botões: #FF3B30 fundo, scale(1.05)

• MODAL PROJETOS POLIDO ✅: Visual coeso e elegante
  - Container: 840px max-width, border-radius 12px, box-shadow profunda
  - Backdrop: blur(4px) para profundidade
  - Hero-image: altura fixa 260px/200px mobile, apenas borda inferior #FCEC07
  - Background: #0F0F1C (melhor contraste que preto absoluto)
  - Título: 32px #FCEC07, underline 3px #FF3B30 width 56px
  - Sub-títulos: 18px #FCEC07 (consistência visual)
  - Copy: 16px/1.6 #E0E0E0, max-width 90ch
  - Setas: círculos 48px, fundo rgba(16,16,16,0.6), stroke 2px #FCEC07
  - Botão fechar: 32px, stroke 2px #FCEC07, sombra hover
  - Progress indicator: "Projeto n/total" no rodapé
  - Animações: scale(0.96→1) + opacity 450ms, setas vibram 2px/8s
  - Acessibilidade: role="dialog", aria-modal, focus-trap, tabindex

❖ ESTRUTURA TÉCNICA ATUAL
• Framework: React + TypeScript + Vite
• Styling: Tailwind CSS + CSS customizado
• Componentes: src/components/ (Hero, About, Portfolio, Process, Testimonials, FixedCTA)
• Páginas: src/pages/ (Index, Projetos, Sobre, Servicos, Contato, Custom404)
• Navegação: React Router DOM
• Acessibilidade: Focus-trap, ARIA labels, keyboard navigation
• Responsividade: Mobile-first, breakpoints 600px/768px

❖ ARQUIVOS PRINCIPAIS
• src/components/FixedCTA.tsx: Strip horizontal com texto deslizante
• src/components/Portfolio.tsx: Modal de projetos polido
• src/index.css: Estilos customizados (modal, CTA, animações)
• src/pages/Index.tsx: Estrutura da homepage
• rules.md: Documentação e memória do projeto

❖ O QUE MANTER
• Hierarquia de seções, IDs, slugs, meta SEO.
• Performance & acessibilidade: contraste ≥ 4.5 : 1, imagens AVIF < 120 KB, foco visível.
• Código já criado deve continuar intacto salvo quando indicado em “ALTERAR”.

• Funcionalidades implementadas: navegação modal, auto-slider, focus-trap, texto deslizante CTA.

❖ O QUE PODE ALTERAR
• Estilos visuais (cores, bordas, animações) desde que respeitem a paleta.
• Conteúdo textual, imagens, microcópias quando solicitado na task.
• Código/JSON apenas da área afetada.

❖ ÚLTIMAS ATUALIZAÇÕES
• CTA Strip Horizontal: Substituição da faixa diagonal por strip com texto deslizante
• Modal Projetos: Polimento visual completo com melhor UX e acessibilidade
• Posicionamento: CTA movido para entre Hero e About para melhor fluxo
• Responsividade: Otimizações para mobile em todos os componentes
• Acessibilidade: ARIA labels, focus-trap e navegação por teclado aprimorados

❖ PRÓXIMAS IMPLEMENTAÇÕES
• PARTE 2: Seção Projetos (headlines + cases)
• PARTE 3: Processo 4 etapas
• PARTE 4: Serviços 3 pacotes
• PARTE 5: FAQs e Contato
• PARTE 6: Assets e imagens IA

❖ STATUS ATUAL
• Servidor: http://localhost:8080/
• Build: Vite + React + TypeScript
• Estado: Desenvolvimento ativo
• Última atualização: Implementação CTA Strip + Modal polido