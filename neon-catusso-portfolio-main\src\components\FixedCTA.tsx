
import { Button } from "@/components/ui/button";

const FixedCTA = () => {
  const openWhatsApp = () => {
    window.open('https://wa.me/5511999999999?text=Olá! Gostaria de agendar um briefing.', '_blank');
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      <div
        className="py-6 px-4"
        style={{
          background: 'linear-gradient(45deg, #FF3B30 0%, #F94646 100%)',
          transform: 'skewY(-4deg)',
          transformOrigin: 'top left'
        }}
      >
        <div className="container mx-auto flex flex-col sm:flex-row items-center justify-between gap-4" style={{ transform: 'skewY(4deg)' }}>
          <div className="text-center sm:text-left">
            <h3 className="font-gt-flexa text-white font-bold text-xl">
              VAMOS CONVERSAR?
            </h3>
          </div>

          <Button
            onClick={openWhatsApp}
            className="bg-white text-black hover:bg-neon-solar font-gt-flexa text-lg px-8 py-3 cursor-custom whitespace-nowrap transition-colors duration-300"
          >
            FALE COMIGO
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FixedCTA;
