
const About = () => {
  return (
    <section id="about" className="py-16 px-4 bg-white">
      <div className="container mx-auto">
        <div className="bg-gray-100 rounded-2xl p-8 max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row items-center gap-8">
            {/* Profile photo */}
            <div className="flex-shrink-0">
              <div className="w-40 h-40 rounded-full border-4 border-tomato-red overflow-hidden bg-gray-300">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
                  alt="Fábio Catusso - Designer UX/UI"
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
            </div>

            {/* About text */}
            <div className="flex-1 text-center md:text-left">
              <h2 className="font-gt-flexa text-dark-bg text-3xl md:text-4xl mb-4">
                Sobre mim
              </h2>
              <p className="text-dark-bg text-lg leading-relaxed mb-4">
                Sou Fábio Catusso, designer UX/UI com 12 anos de experiência transformando ideias complexas em interfaces intuitivas e marcantes. Baseado em São Paulo, trabalho com empresas de todos os portes, desde startups disruptivas até corporações estabelecidas.
              </p>
              <p className="text-dark-bg text-lg leading-relaxed">
                Minha paixão pela tipografia nasceu na faculdade e se tornou minha assinatura – acredito que cada letra conta uma história e cada espaço em branco tem propósito. Especializo-me em criar experiências digitais que não apenas encantam visualmente, mas geram resultados mensuráveis.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
