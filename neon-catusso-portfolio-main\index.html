
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> - Designer UX/UI | São Paulo</title>
    <meta name="description" content="Designer UX/UI há 12 anos em São Paulo. Especialista em interfaces que convertem. Transformo ideias em experiências digitais marcantes." />
    <meta name="author" content="Fábio Catusso" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;900&family=Space+Grotesk:wght@400;500;700&display=swap" rel="stylesheet">

    <meta property="og:title" content="F<PERSON>bio Catusso - Designer UX/UI & Branding" />
    <meta property="og:description" content="Portfolio de Fábio Catusso - Designer especializado em UX/UI e Branding" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
  </head>

  <body>
    <div id="root"></div>

    <!-- synth-wave background -->
    <canvas id="neonGrid" aria-hidden="true"></canvas>
    <script id="neonGridJS">
      /* === grid perspective + moving streaks === */
      (() => {
        const c = document.getElementById('neonGrid');
        const ctx = c.getContext('2d');
        let w, h, t = 0;

        const resize = () => { w = c.width = innerWidth; h = c.height = innerHeight; };
        resize(); addEventListener('resize', resize);

        function draw(){
          ctx.clearRect(0,0,w,h);
          ctx.save();
          /* perspective grid */
          ctx.strokeStyle='#2211ff33'; ctx.lineWidth=1;
          const step = 40, horizon = h*0.55;
          for(let y = horizon; y < h; y += step){
            ctx.beginPath();
            ctx.moveTo(0,y); ctx.lineTo(w,y);
            ctx.stroke();
          }
          const vLines = 60;
          for(let i=0;i<=vLines;i++){
            const x = i/vLines*w;
            ctx.beginPath();
            ctx.moveTo(w/2,0);
            ctx.lineTo(x,h);
            ctx.stroke();
          }
          /* moving streaks */
          const streaks = 35;
          for(let i=0;i<streaks;i++){
            const speed = 0.15 + (i%5)*.02;
            const x = ( (i*200) + (t*speed*200) ) % (w+200) -100;
            const y = h*0.3 + Math.sin((i+t)*.3)*h*0.15;
            ctx.strokeStyle = i%2 ? '#ff4c9a' : '#00c8ff';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x,y); ctx.lineTo(x+80,y);
            ctx.stroke();
          }
          ctx.restore(); t+=0.016;
          requestAnimationFrame(draw);
        }
        draw();
      })();
    </script>

    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
