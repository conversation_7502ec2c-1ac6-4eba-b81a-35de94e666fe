
import { useState, useEffect } from 'react';

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      company: "TechStart",
      text: "Fábio transformou completamente nossa presença digital. O design criado superou todas as expectativas e aumentou significativamente nossa conversão.",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
    },
    {
      id: 2,
      name: "<PERSON>",
      company: "Inovação Digital",
      text: "Profissional excepcional! Entendeu perfeitamente nossa visão e entregou um projeto que elevou nossa marca a outro patamar.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
    },
    {
      id: 3,
      name: "<PERSON>",
      company: "Creative Studio",
      text: "O trabalho do Fábio é simplesmente incrível. Atenção aos detalhes, criatividade e profissionalismo definem seu trabalho.",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
    }
  ];

  // Auto-play slider every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  const current = testimonials[currentTestimonial];

  return (
    <section className="py-16 px-4 w-full" style={{ backgroundColor: '#0F0F1C' }}>
      <div className="container mx-auto max-w-4xl">
        <h2 className="font-gt-flexa text-neon-solar text-4xl md:text-5xl text-center mb-12">
          DEPOIMENTOS
        </h2>

        {/* Card de depoimento 600×220px */}
        <div className="mx-auto max-w-[600px] h-[220px] flex items-center justify-center">
          <div
            className="w-full h-full border-2 rounded-2xl p-6 text-center flex flex-col justify-center testimonial-card-glow"
            style={{
              borderColor: '#FCEC07',
              backgroundColor: 'rgba(15, 15, 28, 0.8)'
            }}
          >
            <div className="flex flex-col items-center space-y-3">
              {/* Foto cliente 64px circular */}
              <img
                src={current.image}
                alt={current.name}
                className="w-16 h-16 rounded-full border-2"
                style={{ borderColor: '#FF3B30' }}
                loading="lazy"
              />

              {/* Texto do depoimento */}
              <blockquote className="text-text-white text-sm leading-relaxed font-space-grotesk max-w-md">
                "{current.text}"
              </blockquote>

              {/* Nome e empresa */}
              <div>
                <p className="font-gt-flexa text-neon-solar text-sm">
                  {current.name}
                </p>
                <p className="text-border-gray text-xs">
                  {current.company}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Dots navigation */}
        <div className="flex justify-center space-x-3 mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-colors duration-300 cursor-custom ${
                index === currentTestimonial ? 'bg-neon-solar' : 'bg-gray-500'
              }`}
              aria-label={`Depoimento ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
